package com.sankuai.deepcode.analysis.server.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.deepcode.ai.enums.ChatEnum;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMessage;
import com.sankuai.deepcode.ai.llm.model.chat.ChatMsgRes;
import com.sankuai.deepcode.ai.llm.openai.chat.Role;
import com.sankuai.deepcode.ai.llm.service.OneApiService;
import com.sankuai.deepcode.ai.prompt.PromptTemplate;
import com.sankuai.deepcode.analysis.server.domain.AiReviewResponse;
import com.sankuai.deepcode.ast.enums.DiffTypeEnum;
import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.commons.JacksonUtils;
import com.sankuai.deepcode.dao.domain.*;
import com.sankuai.deepcode.dao.enums.ItemStatusEnum;
import com.sankuai.deepcode.dao.enums.ItemStepEnum;
import com.sankuai.deepcode.dao.model.AiReviewTaskExtern;
import com.sankuai.deepcode.dao.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Package: com.sankuai.deepcode.analysis.server.service
 * Description:
 *
 * <AUTHOR>
 * @since 2025/5/29 16:04
 */
@Service
@Slf4j
public class AiReviewTaskService {
    private static final String AI_REVIEW_PROMPT_KEY = "ai_review_prompt";

    @Autowired
    private CodeViewAnalysisService codeViewAnalysisService;
    @Autowired
    private ItemSatusService itemSatusService;
    @Autowired
    private DcProjectService dcProjectService;
    @Autowired
    private RuleBindInfoService ruleBindInfoService;
    @Autowired
    private CodeFileAnalysisService codeFileAnalysisService;
    @Autowired
    private DcPromptTemplateService dcPromptTemplateService;
    @Autowired
    private RuleInfoDetailService ruleInfoDetailService;
    @Autowired
    private RuleTaskDetailService ruleTaskDetailService;

    @Autowired
    private OneApiService oneApiService;

    ChatEnum chatEnum = ChatEnum.GPT_4_1_MINI;

    public Future<Boolean> aiReviewByItem(CodeAnalysisItem codeAnalysisItem) {
        long start = System.currentTimeMillis();
        try {
            itemSatusService.updateItemAsyncStatus(codeAnalysisItem, ItemStatusEnum.PROCESSING);
            itemSatusService.insertDetail(codeAnalysisItem.getId(), ItemStepEnum.AI_REVIEW, "开始代码智能审查", "");

            DcProject project = dcProjectService.getProjectByItemId(codeAnalysisItem.getId());
            if (project == null) {
                log.warn("aiReviewByItem project is null, itemId: {}", codeAnalysisItem);
                // 项目为空的时候，无法获取到有效用户配置，不进行智能评审
                itemSatusService.updateDetail(codeAnalysisItem.getId(), ItemStepEnum.AI_REVIEW, "代码智能审查异常[项目为空]", ItemStatusEnum.SUCCESS);
                return new AsyncResult<>(true);
            }

            //  获取项目绑定的规则
            List<RuleBindInfo> ruleBindInfos = ruleBindInfoService.getRuleBindInfoByProjectId(project.getId());
            //  获取用户绑定的规则
            List<RuleBindInfo> ruleUserInfos = ruleBindInfoService.getRuleBindInfoByUserId(project.getUserId());

            // 合并项目和用户规则列表，按ruleId去重
            List<RuleBindInfo> finalRules = Stream.concat(ruleBindInfos.stream(), ruleUserInfos.stream())
                    .collect(Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RuleBindInfo::getRuleId))),
                            ArrayList::new
                    ));

            if (CollectionUtils.isEmpty(finalRules)) {
                log.warn("aiReviewByItem finalRules is empty, itemId: {}", codeAnalysisItem);
                // 合并后的规则列表为空，不进行智能评审
                itemSatusService.updateDetail(codeAnalysisItem.getId(), ItemStepEnum.AI_REVIEW, "代码智能评审规则列表为空", ItemStatusEnum.SUCCESS);
                return new AsyncResult<>(true);
            }

            List<RuleInfoDetail> ruleInfoDetails = ruleInfoDetailService.getRuleInfoDetailsByRuleIds(finalRules.stream().map(RuleBindInfo::getRuleId).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(ruleInfoDetails)) {
                log.warn("aiReviewByItem ruleInfoDetails is empty, itemId: {}", codeAnalysisItem);
                // 规则详情列表为空，不进行智能评审
                itemSatusService.updateDetail(codeAnalysisItem.getId(), ItemStepEnum.AI_REVIEW, "代码智能评审规则详情错误", ItemStatusEnum.SUCCESS);
                return new AsyncResult<>(true);
            }
            // 按照规则类型分组, 0-链路风险  1-编码规则
            Map<Integer, List<RuleInfoDetail>> ruleTypeMap = ruleInfoDetails.stream().collect(Collectors.groupingBy(RuleInfoDetail::getType));

            List<CodeFileAnalysis> diffCodeFiles = codeFileAnalysisService.getDiffCodeFilesByItemId(codeAnalysisItem.getId());
            if (CollectionUtils.isEmpty(diffCodeFiles)) {
                log.warn("aiReviewByItem diffCodeFiles is empty, itemId: {}", codeAnalysisItem);
                // 差异代码文件列表为空，不进行智能评审
                itemSatusService.updateDetail(codeAnalysisItem.getId(), ItemStepEnum.AI_REVIEW, "代码智能评审变更文件为空", ItemStatusEnum.SUCCESS);
                return new AsyncResult<>(true);
            }

            AiReviewTaskExtern aiReviewExtern = new AiReviewTaskExtern();
            aiReviewExtern.setStatus(ItemStatusEnum.PROCESSING.getCode());
            aiReviewExtern.setModel(chatEnum.getName());
            aiReviewExtern.setTotalNum(diffCodeFiles.size());
            aiReviewExtern.setRunNum(0);
            aiReviewExtern.setPercent(0);

            for (CodeFileAnalysis codeFile : diffCodeFiles) {
                aiReviewExtern.setRunNum(aiReviewExtern.getRunNum() + 1);
                //  获取差异代码文件的内容
                String diffFileContent = getDiffFileContent(codeFile);

                if (StringUtils.isBlank(diffFileContent)) {
                    log.warn("aiReviewByItem diffFileContent is blank, itemId: {}, diffCodeFile: {}", codeAnalysisItem, codeFile);
                    continue;
                }

                ruleTypeMap.forEach((ruleType, ruleInfoDetailList) -> {
                    switch (ruleType) {
                        // 链路风险
                        case 0:
                            // 按照变更文件 -> 变更方法 -> 变更方法的入口方法（当前是找到没有被调用的方法，无 入口方法打标 逻辑）
                            break;

                        // 编码规则
                        case 1:
                            String ruleContent = assembleRules(ruleInfoDetailList, codeFile);
                            if (StringUtils.isBlank(ruleContent)) {
                                log.warn("aiReviewByItem ruleContent is blank, itemId: {}, diffCodeFile: {}", codeAnalysisItem, codeFile);
                                return;
                            }

                            PromptTemplate promptTemplate = getSystemPrompt();
                            String prompt = promptTemplate.format(ImmutableMap.of(
                                    "code_diff_placeholder", diffFileContent,
                                    "bind_rules_placeholder", ruleContent
                            )).toString();

                            List<ChatMessage> chatMessages = Lists.newArrayList();
                            chatMessages.add(new ChatMessage(Role.USER.name().toLowerCase(), prompt));

                            ChatMsgRes chatMsgRes = oneApiService.completions(chatMessages, ChatEnum.GPT_4_1_MINI.getName());
                            String response = chatMsgRes.getChoices().get(0).getMessage().getContent();
                            log.info("aiReviewByItem response: {}", chatMsgRes);
                            if (StringUtils.isBlank(response)) {
                                log.warn("aiReviewByItem response is blank, itemId: {}, diffCodeFile: {}", codeAnalysisItem, codeFile);
                                return;
                            }
                            Optional<List<AiReviewResponse>> aiReviewResponse = JacksonUtils.readValue(response, new TypeReference<List<AiReviewResponse>>() {
                            });
                            if (!aiReviewResponse.isPresent() || CollectionUtils.isEmpty(aiReviewResponse.get())) {
                                log.warn("aiReviewByItem aiReviewResponse is not present, itemId: {}, diffCodeFile: {}", codeAnalysisItem, codeFile);
                                return;
                            }
                            aiReviewExtern.setIssueFiles(aiReviewExtern.getIssueFiles() + 1);

                            aiReviewResponse.get().forEach(r -> {
                                RuleTaskDetail ruleTaskDetail = new RuleTaskDetail();
                                ruleTaskDetail.setItemId(codeFile.getItemId())
                                        .setRuleId(r.getRuleId())
                                        .setFileVid(codeFile.getFileVid())
                                        .setStartLine(r.getLineNumber())
                                        .setType(ruleType)
                                        .setValid(true)
                                        .setValue("【行范围】：" + r.getLineRange() + "\n"
                                                + "【问题】：" + r.getIssueExplanation() + "\n"
                                                + "【修复建议】：" + r.getFixSuggestion() + "\n"
                                        );
                                ruleTaskDetailService.save(ruleTaskDetail);
                            });

                            break;
                        //  其他未识别的情况
                        default:
                            log.warn("aiReviewByItem ruleType is unknown, itemId: {}, ruleType: {}", codeAnalysisItem, ruleType);
                            break;
                    }
                });

                if (aiReviewExtern.getRunNum() % 2 == 0) {
                    double percent = Math.round((double) aiReviewExtern.getRunNum() / aiReviewExtern.getTotalNum() * 10000) / 100.0;
                    aiReviewExtern.setPercent(percent);
                    itemSatusService.updateDetailAsyncExtern(codeAnalysisItem.getId(), ItemStepEnum.KNOWLEDGE, JSON.toJSONString(aiReviewExtern));
                    log.info("analysisByItem item:{} ai review percent:{}", codeAnalysisItem.getId(), percent);
                }
            }

            aiReviewExtern.setStatus(ItemStatusEnum.SUCCESS.getCode());
            aiReviewExtern.setPercent(100.0);
            long cost = System.currentTimeMillis() - start;
            aiReviewExtern.setCostTime(cost);
            itemSatusService.updateDetailSucess(codeAnalysisItem.getId(), ItemStepEnum.AI_REVIEW, "代码智能审查完成", JSON.toJSONString(aiReviewExtern));
            itemSatusService.updateItemAsyncSucess(codeAnalysisItem, cost);

        } catch (Exception e) {
            log.error("aiReviewByItem error: {}", e.getMessage(), e);
            itemSatusService.updateDetailFail(codeAnalysisItem.getId(), ItemStepEnum.AI_REVIEW, "代码智能审查异常");
            itemSatusService.updateItemAsyncFail(codeAnalysisItem, System.currentTimeMillis() - start);
        }
        return new AsyncResult<>(true);
    }

    /**
     * 依据变更文件，组织出差异代码
     *
     * @param codeFile 代码文件
     * @return 差异代码
     */
    public String getDiffFileContent(CodeFileAnalysis codeFile) {
        if (codeFile == null) {
            log.warn("getDiffFileContent codeFile is null");
            return null;
        }

        StringBuilder diffBuilder = new StringBuilder();

        // 添加文件路径信息，类似 git diff 的格式
        diffBuilder.append("--- a/").append(codeFile.getFilePath()).append("\n");
        diffBuilder.append("+++ b/").append(codeFile.getFilePath()).append("\n");

        try {
            // 获取文件的所有代码视图
            List<CodeViewAnalysis> codeViewAnalyses = codeViewAnalysisService.getBySourceVid(codeFile.getItemId(), codeFile.getFileVid());
            if (CollectionUtils.isEmpty(codeViewAnalyses)) {
                log.warn("getDiffFileContent codeViewAnalyses is empty, fileVid: {}", codeFile.getFileVid());
                return null;
            }

            // 转换为 CodeView 对象
            List<CodeView> codeViews = covertCodeView(codeViewAnalyses);
            if (CollectionUtils.isEmpty(codeViews)) {
                log.warn("getDiffFileContent codeViews is empty, fileVid: {}", codeFile.getFileVid());
                return null;
            }
            // 对 CodeView 按行号排序，确保顺序正确
            codeViews.sort(Comparator.comparing(CodeView::getLine));

            // 去除重复的行（相同行号和类型的行）
            codeViews = removeDuplicateLines(codeViews);

            // 分组处理变更块
            List<List<CodeView>> changeBlocks = new ArrayList<>();
            List<CodeView> currentBlock = null;

            for (int i = 0; i < codeViews.size(); i++) {
                CodeView codeView = codeViews.get(i);

                // 如果是变更行（新增、删除、冲突）
                if (codeView.getType() != DiffTypeEnum.SAM.getCode()) {
                    // 如果当前没有活跃的变更块，创建一个新的
                    if (currentBlock == null) {
                        currentBlock = new ArrayList<>();

                        // 添加上下文（前3行）
                        int contextStart = Math.max(0, i - 3);
                        for (int j = contextStart; j < i; j++) {
                            currentBlock.add(codeViews.get(j));
                        }
                    }

                    // 添加当前变更行
                    currentBlock.add(codeView);
                } else if (currentBlock != null) {
                    // 如果是无变更行，且当前有活跃的变更块
                    // 添加上下文（后3行）
                    currentBlock.add(codeView);

                    // 检查后面是否还有变更行
                    boolean hasMoreChanges = false;
                    for (int j = i + 1; j < Math.min(codeViews.size(), i + 3); j++) {
                        if (codeViews.get(j).getType() != DiffTypeEnum.SAM.getCode()) {
                            hasMoreChanges = true;
                            break;
                        }
                    }

                    // 如果后面3行内没有变更行，结束当前变更块
                    if (!hasMoreChanges && currentBlock.size() >= 7) {
                        // 至少包含前3行+变更行+后3行
                        changeBlocks.add(currentBlock);
                        currentBlock = null;
                    }
                }
            }

            // 处理最后一个变更块
            if (currentBlock != null) {
                changeBlocks.add(currentBlock);
            }
            // 合并相邻或重叠的变更块
            changeBlocks = mergeOverlappingBlocks(changeBlocks);

            // 格式化输出变更块
            for (List<CodeView> block : changeBlocks) {
                // 计算变更块的行号范围
                int oldStart = 0;
                int oldCount = 0;
                int newStart = 0;
                int newCount = 0;

                for (int i = 0; i < block.size(); i++) {
                    CodeView view = block.get(i);

                    // 找到第一个有效行号
                    if (i == 0) {
                        if (view.getLine() > 0) {
                            oldStart = view.getLine();
                            newStart = view.getLine();
                        }
                    }

                    // 计算行数
                    if (view.getType() == DiffTypeEnum.SAM.getCode() ||
                            view.getType() == DiffTypeEnum.DEL.getCode()) {
                        oldCount++;
                    }

                    if (view.getType() == DiffTypeEnum.SAM.getCode() ||
                            view.getType() == DiffTypeEnum.ADD.getCode()) {
                        newCount++;
                    }
                }

                // 添加变更块头信息
                diffBuilder.append("@@ -").append(oldStart).append(",").append(oldCount)
                        .append(" +").append(newStart).append(",").append(newCount)
                        .append(" @@\n");
                // 添加变更块内容
                for (CodeView view : block) {
                    switch (view.getType()) {
                        case 1: // SAM
                            diffBuilder.append(" ").append(view.getView()).append("\n");
                            break;
                        case 2: // ADD
                            diffBuilder.append("+").append(view.getView()).append("\n");
                            break;
                        case 3: // DEL
                            diffBuilder.append("-").append(view.getView()).append("\n");
                            break;
                        default:
                            log.warn("getDiffFileContent unknown type: {}", view.getType());
                            break;
                    }
                }

                diffBuilder.append("\n");
            }

            return diffBuilder.toString();
        } catch (Exception e) {
            log.error("getDiffFileContent error: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 去除重复的行（相同行号和类型的行）
     *
     * @param codeViews 原始代码视图列表
     * @return 去重后的代码视图列表
     */
    private List<CodeView> removeDuplicateLines(List<CodeView> codeViews) {
        Map<String, CodeView> uniqueLines = new LinkedHashMap<>();

        for (CodeView view : codeViews) {
            // 使用行号和内容作为唯一键
            String key = view.getLine() + "_" + view.getType() + "_" + view.getView();
            if (!uniqueLines.containsKey(key)) {
                uniqueLines.put(key, view);
            }
        }

        return new ArrayList<>(uniqueLines.values());
    }

    /**
     * 合并重叠的变更块
     *
     * @param blocks 原始变更块列表
     * @return 合并后的变更块列表
     */
    private List<List<CodeView>> mergeOverlappingBlocks(List<List<CodeView>> blocks) {
        if (blocks.size() <= 1) {
            return blocks;
        }
        // 按照第一个元素的行号排序
        blocks.sort((b1, b2) -> {
            if (b1.isEmpty() || b2.isEmpty()) {
                return 0;
            }
            return Integer.compare(b1.get(0).getLine(), b2.get(0).getLine());
        });
        List<List<CodeView>> mergedBlocks = new ArrayList<>();
        List<CodeView> currentBlock = new ArrayList<>(blocks.get(0));
        for (int i = 1; i < blocks.size(); i++) {
            List<CodeView> nextBlock = blocks.get(i);
            // 获取当前块的最后一行和下一个块的第一行
            int currentEnd = currentBlock.get(currentBlock.size() - 1).getLine();
            int nextStart = nextBlock.get(0).getLine();
            // 如果两个块相距不超过7行（上下文各3行+至少1行变更），则合并
            if (nextStart - currentEnd <= 7) {
                // 找到不重复的行添加到当前块
                for (CodeView view : nextBlock) {
                    boolean isDuplicate = false;
                    for (CodeView existingView : currentBlock) {
                        if (existingView.getLine() == view.getLine() &&
                                existingView.getType() == view.getType() &&
                                existingView.getView().equals(view.getView())) {
                            isDuplicate = true;
                            break;
                        }
                    }
                    if (!isDuplicate) {
                        currentBlock.add(view);
                    }
                }
                // 重新按行号排序
                currentBlock.sort(Comparator.comparing(CodeView::getLine));
            } else {
                // 如果不能合并，保存当前块并开始新块
                mergedBlocks.add(currentBlock);
                currentBlock = new ArrayList<>(nextBlock);
            }
        }
        // 添加最后一个处理的块
        mergedBlocks.add(currentBlock);
        return mergedBlocks;
    }

    /**
     * 将 CodeViewAnalysis 转换为 CodeView
     *
     * @param codeViewAnalyses 代码视图分析列表
     * @return 代码视图列表
     */
    private List<CodeView> covertCodeView(List<CodeViewAnalysis> codeViewAnalyses) {
        List<CodeView> codeViews = new ArrayList<>();
        for (CodeViewAnalysis codeViewAnalysis : codeViewAnalyses) {
            CodeView codeView = new CodeView();
            codeView.setId(codeViewAnalysis.getCodeId());
            codeView.setLine(codeViewAnalysis.getCodeLine());
            codeView.setType(codeViewAnalysis.getCodeType());
            codeView.setView(codeViewAnalysis.getCodeView());
            codeViews.add(codeView);
        }
        return codeViews;
    }

    /**
     * 获取系统提示词
     * FIXME: 后续添加缓存
     *
     * @return 系统提示词对象
     */
    private PromptTemplate getSystemPrompt() {
        return PromptTemplate.of(
                dcPromptTemplateService.getPromptTemplateByKey(AI_REVIEW_PROMPT_KEY).getContent()
        );
    }

    /**
     * 组装规则信息, 可能会根据规则类型进行不同的处理， 比如链路风险规则需要按照规则类型进行分组
     * 不同语言类型过滤等
     *
     * @param ruleBindInfos 规则列表
     * @param codeFile      代码文件
     * @return 规则内容
     */
    private String assembleRules(List<RuleInfoDetail> ruleBindInfos, CodeFileAnalysis codeFile) {
        if (CollectionUtils.isEmpty(ruleBindInfos)) {
            return "";
        }
        return ruleBindInfos.stream()
                .filter(rule -> rule.getLanguages().contains(codeFile.getFileType()))
                .map(ruleBindInfo -> ruleBindInfo.getId() + ")." + ruleBindInfo.getValue())
                .collect(Collectors.joining("\n\n"));
    }
}

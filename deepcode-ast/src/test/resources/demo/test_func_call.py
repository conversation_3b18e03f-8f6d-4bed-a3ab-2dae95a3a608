import os
from typing import Optional, List

from dotenv import load_dotenv

import operator
import struct
import uuid
from contextvars import ContextVar
from loguru import logger



def get_from_env(name, default: Optional[str] = None) -> str:
    if name in os.environ and (r := os.environ[name]):
        return r
    elif default is not None:
        return default
    else:
        raise ValueError(
            f"Did not find {name}, please add an environment variable or config in .env file"
        )


def get_list_from_env(name, separator: str = ',', default: Optional[List[str]] = None) -> List[str]:
    list_str = get_from_env(name, "")
    if not list_str:
        if default is None:
            default = []
        return default
    return [_.strip() for _ in list_str.split(separator) if _]


BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

env_map = {
    "LOCAL": "local.env",
    "TEST": "test.env"
}

env = os.environ.get("ENV") or "LOCAL"
env_file = env_map.get(env, "local.env")
load_dotenv(os.path.join(BASE_DIR, env_file))


def is_local():
    return env == "LOCAL"


def is_test():
    return env == "TEST"


def is_write_log_file():
    return get_from_env("WRITE_LOG_FILE") == "True"


APPKEY = get_from_env("APPKEY")

LOG_IGNORE_URIS = get_list_from_env("LOG_IGNORE_URI")

# Lambda expressions test
filter_func = lambda x: get_from_env(x) is not None
transform_func = lambda data: os.path.join(BASE_DIR, data)
complex_lambda = lambda a, b: get_from_env(a) + str(len(b))

_trace_id: ContextVar[str] = ContextVar("m-trace", default="")

def gen_trace_id() -> int:
    """
    生成全局唯一的 TraceId
    :return:
    """
    return operator.xor(*struct.unpack(">qq", uuid.uuid1().bytes))


class TraceId:
    @staticmethod
    def set_trace() -> ContextVar[str]:
        _trace_id.set(F"{gen_trace_id()}")
        return _trace_id

    @staticmethod
    def get_trace() -> str:
        result = _trace_id.get()
        if not result:
            logger.warning(F"获取到的Trace未初始化：trace=【{result}】，兜底调用一次初始化：{TraceId.set_trace()}")
            return TraceId.get_trace()
        return result

".".join(list)

{"a": 1, "b": 3}.get(x)

{1,3,4}.union(set([2,4,5]))

(1,3,4,4).count(4)

True.bit_count()
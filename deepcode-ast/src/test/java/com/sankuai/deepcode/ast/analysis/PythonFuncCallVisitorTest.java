package com.sankuai.deepcode.ast.analysis;

import com.sankuai.deepcode.ast.model.python3.PythonAnalysesResult;
import com.sankuai.deepcode.ast.python.test.TestPython3Visitor;
import com.sankuai.deepcode.ast.python3.analysis.PythonAnalyzer;
import lombok.SneakyThrows;
import org.testng.annotations.Test;

import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Package: com.sankuai.deepcode.ast.python3.analysis
 * Description:
 *
 * <AUTHOR>
 * @since 2025/1/13 14:49
 */
public class PythonFuncCallVisitorTest extends BaseTest {
    @SneakyThrows
    @Test
    public void testVisitFunctionCall() {
        PythonAnalysesResult result = new PythonAnalysesResult();

        Path classPath = Paths.get(TestPython3Visitor.class.getResource("/").toURI().getPath() + "test_module");
        Path pythonFilePath = classPath.resolve("utils.py");

        PythonAnalyzer.analyzePythonFile(classPath, pythonFilePath, result);

        prettyPrint(result);
    }


    @SneakyThrows
    @Test
    public void testVisitFunctionCall1() {
        PythonAnalysesResult result = new PythonAnalysesResult();

        Path classPath = Paths.get(TestPython3Visitor.class.getResource("/").toURI().getPath() + "demo");
        Path pythonFilePath = classPath.resolve("test_func_call.py");

        PythonAnalyzer.analyzePythonFile(classPath, pythonFilePath, result);

        prettyPrint(result);
    }
}

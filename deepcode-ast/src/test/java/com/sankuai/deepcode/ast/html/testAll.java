package com.sankuai.deepcode.ast.html;

import com.sankuai.deepcode.ast.foreend.ForeEndAnalysis;

public class testAll {

    public static void main(String[] args) throws Exception {
        ForeEndAnalysis foreEndAnalysis = new ForeEndAnalysis();
        String gitUrl = "ssh://*******************/bm/banma_mrn.git";
        String repos = "banma_mrn";
        String fromBranch = "master";
        String toBranch = "master";
        String buildBranch = "master";
        String buildCommit = "";
        foreEndAnalysis.analysis(0, gitUrl, repos, fromBranch, toBranch, buildBranch, buildCommit);
    }
}

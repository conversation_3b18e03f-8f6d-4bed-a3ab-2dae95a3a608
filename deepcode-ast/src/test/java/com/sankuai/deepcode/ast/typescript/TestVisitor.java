package com.sankuai.deepcode.ast.typescript;

import com.sankuai.deepcode.ast.typescript.gen.TypeScriptLexer;
import com.sankuai.deepcode.ast.typescript.gen.TypeScriptParser;
import com.sankuai.deepcode.ast.typescript.visitor.MyTypeScriptVisitor;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.atn.PredictionMode;
import org.antlr.v4.runtime.misc.ParseCancellationException;
import org.antlr.v4.runtime.tree.ParseTree;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class TestVisitor {

    public static void main(String[] args) throws IOException {
       for(int i=0;i<10;i++){
           String cssFilePath = "/Users/<USER>/idea/git/banma_mrn/rn_banma_setting/src/main/voiceSetting/voiceMainSetting/VoiceMainSettingPage.tsx";

           String code = new String(Files.readAllBytes(Paths.get(cssFilePath)));
           TypeScriptLexer lexer = new TypeScriptLexer(CharStreams.fromString(code));
           CommonTokenStream tokens = new CommonTokenStream(lexer);
           TypeScriptParser parser = new TypeScriptParser(tokens);
//        parser.setBuildParseTree(false);
//        parser.removeErrorListeners(); // 移除默认监听器
//        parser.addErrorListener(new DiagnosticErrorListener());
//        parser.getInterpreter().setPredictionMode(PredictionMode.LL_EXACT_AMBIG_DETECTION); // 精确模式
//
//


           long t1 = System.currentTimeMillis();
           ParseTree tree = parser.program();


//        // 尝试强制使用SLL模式（更快但容错低）
//        parser.getInterpreter().setPredictionMode(PredictionMode.SLL);
//// 若失败再回退到LL模式
//        try {
//            parser.setErrorHandler(new BailErrorStrategy());
//            tree = parser.program();
//        } catch (ParseCancellationException e) {
//            tokens.reset();
//            parser.setErrorHandler(new DefaultErrorStrategy());
//            parser.getInterpreter().setPredictionMode(PredictionMode.LL);
//            tree = parser.program();
//        }

           long t2 = System.currentTimeMillis();
           System.out.println("parse time:" + (t2 - t1));
           MyTypeScriptVisitor visitor = new MyTypeScriptVisitor();
           visitor.visit(tree);
           long t3 = System.currentTimeMillis();
           System.out.println("visit time:" + (t3 - t2));
       }


        System.out.println();
    }
}

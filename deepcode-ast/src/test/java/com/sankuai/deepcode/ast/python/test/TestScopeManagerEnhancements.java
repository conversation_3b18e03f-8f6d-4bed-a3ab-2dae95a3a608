package com.sankuai.deepcode.ast.python.test;

import com.sankuai.deepcode.ast.model.python3.*;

import java.util.*;

/**
 * 测试 ScopeManager 增强功能和 handleAnalyzeResult 核心逻辑
 */
public class TestScopeManagerEnhancements {

    public void testBasicFunctionality() {
        System.out.println("=== 测试基本功能 ===");
        
        // 模拟本地模块列表
        Set<String> localModules = new HashSet<>();
        localModules.add("com.example.module1");
        localModules.add("com.example.module2");
        
        // 测试模块判断逻辑
        testModuleClassification(localModules);
        
        // 测试 ScopeManager 新增功能
        testScopeManagerEnhancements();
        
        System.out.println("基本功能测试完成");
    }

    public void testStarImportHandling() {
        System.out.println("=== 测试 Star Import 处理功能 ===");
        
        // 测试 ScopeManager 的符号导入功能
        testSymbolImport();
        
        System.out.println("Star Import 测试完成");
    }

    /**
     * 测试模块分类逻辑
     */
    private void testModuleClassification(Set<String> localModules) {
        System.out.println("测试模块分类逻辑:");
        
        // 测试用例
        String[] testModules = {
            "com.example.module1",           // 本地模块
            "com.example.module2",           // 本地模块
            "com.example.module1.submodule", // 本地子模块
            "numpy",                         // 外部模块
            "pandas",                        // 外部模块
            "com.other.module"               // 外部模块
        };
        
        for (String module : testModules) {
            boolean isLocal = isLocalModule(module, localModules);
            String type = isLocal ? "local" : "unknown";
            System.out.println("  " + module + " -> " + type);
        }
    }

    /**
     * 测试 ScopeManager 新增功能
     */
    private void testScopeManagerEnhancements() {
        System.out.println("测试 ScopeManager 新增功能:");
        
        // 创建测试 ScopeManager
        ScopeManager scopeManager = new ScopeManager("test.module");
        
        // 添加一些符号
        scopeManager.addToScope("function1", "test.module.function1", 1);
        scopeManager.addToScope("variable1", "test.module.variable1", 2);
        scopeManager.addToScope("class1", "test.module.class1", 3);
        
        // 测试新增的方法
        Set<String> scopeKeys = scopeManager.getScopeKeySet();
        System.out.println("  作用域列表: " + scopeKeys);
        
        Set<String> variableNames = scopeManager.getVariableNamesInScope("test.module");
        System.out.println("  变量名称: " + variableNames);
        
        String currentScope = scopeManager.getCurrentScope();
        System.out.println("  当前作用域: " + currentScope);
        
        boolean hasScope = scopeManager.hasScopeNamed("test.module");
        System.out.println("  是否存在作用域 'test.module': " + hasScope);
    }

    /**
     * 测试符号导入功能
     */
    private void testSymbolImport() {
        System.out.println("测试符号导入功能:");
        
        // 创建源 ScopeManager
        ScopeManager sourceScope = new ScopeManager("source.module");
        sourceScope.addToScope("exportedFunction", "source.module.exportedFunction", 1);
        sourceScope.addToScope("exportedVariable", "source.module.exportedVariable", 2);
        sourceScope.addToScope("exportedClass", "source.module.exportedClass", 3);
        
        // 创建目标 ScopeManager
        ScopeManager targetScope = new ScopeManager("target.module");
        
        // 执行符号导入
        targetScope.importAllSymbolsFromScope(sourceScope, "source.module", 10);
        
        // 验证导入结果
        String resolvedFunction = targetScope.resolveSymbol("exportedFunction", 15);
        String resolvedVariable = targetScope.resolveSymbol("exportedVariable", 15);
        String resolvedClass = targetScope.resolveSymbol("exportedClass", 15);
        
        System.out.println("  导入后解析 exportedFunction: " + resolvedFunction);
        System.out.println("  导入后解析 exportedVariable: " + resolvedVariable);
        System.out.println("  导入后解析 exportedClass: " + resolvedClass);
        
        // 验证是否成功导入
        boolean functionImported = !resolvedFunction.equals("Unknown");
        boolean variableImported = !resolvedVariable.equals("Unknown");
        boolean classImported = !resolvedClass.equals("Unknown");
        
        System.out.println("  符号导入成功: " + (functionImported && variableImported && classImported));
    }

    /**
     * 判断模块是否为本地模块
     */
    private boolean isLocalModule(String modulePath, Set<String> localModules) {
        if (modulePath == null || modulePath.isEmpty()) {
            return false;
        }
        
        // 直接匹配
        if (localModules.contains(modulePath)) {
            return true;
        }
        
        // 检查是否为本工程模块的子模块
        for (String localModule : localModules) {
            if (modulePath.startsWith(localModule + ".") || localModule.startsWith(modulePath + ".")) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 测试完整的 handleAnalyzeResult 逻辑
     */
    public void testCompleteLogic() {
        System.out.println("=== 测试完整逻辑 ===");
        
        // 创建模拟的分析结果
        Map<String, ScopeManager> scopeManagerMap = new HashMap<>();
        scopeManagerMap.put("com.example.module1", new ScopeManager("com.example.module1"));
        scopeManagerMap.put("com.example.module2", new ScopeManager("com.example.module2"));
        scopeManagerMap.put("external.library", new ScopeManager("external.library"));
        
        // 获取本地模块列表
        Set<String> localModules = scopeManagerMap.keySet();
        System.out.println("本地模块列表: " + localModules);
        
        // 模拟处理导入节点
        System.out.println("模拟处理导入节点:");
        String[] importModules = {"com.example.module2", "numpy", "pandas", "com.example.module1"};
        for (String importModule : importModules) {
            String source = isLocalModule(importModule, localModules) ? "local" : "unknown";
            System.out.println("  导入 " + importModule + " -> " + source);
        }
        
        // 模拟处理函数调用节点
        System.out.println("模拟处理函数调用节点:");
        String[] callModules = {"com.example.module1", "numpy.random", "com.example.module2", "requests"};
        for (String callModule : callModules) {
            String source = isLocalModule(callModule, localModules) ? "local" : "unknown";
            System.out.println("  调用 " + callModule + " -> " + source);
        }
        
        System.out.println("完整逻辑测试完成");
    }

    public static void main(String[] args) {
        TestScopeManagerEnhancements test = new TestScopeManagerEnhancements();
        
        test.testBasicFunctionality();
        System.out.println();
        
        test.testStarImportHandling();
        System.out.println();
        
        test.testCompleteLogic();
        System.out.println();
        
        System.out.println("所有测试通过！");
        System.out.println("ScopeManager 增强功能和 handleAnalyzeResult 核心逻辑验证成功！");
    }
}

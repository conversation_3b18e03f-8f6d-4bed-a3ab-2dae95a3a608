package com.sankuai.deepcode.ast.html;

import com.sankuai.deepcode.ast.foreend.ForeEndAnalysis;
import com.sankuai.deepcode.ast.html.gen.HTMLLexer;
import com.sankuai.deepcode.ast.html.gen.HTMLParser;
import com.sankuai.deepcode.ast.html.visitor.MyHtmlVisitor;
import com.sankuai.deepcode.ast.model.html.HtmlNode;
import com.sankuai.deepcode.ast.model.scss.ScssNode;
import com.sankuai.deepcode.ast.model.typescript.ScriptImport;
import com.sankuai.deepcode.ast.model.typescript.ScriptNode;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TestVisitor {

    public static void main(String[] args) throws IOException {
        String xmlFilePath = "/Users/<USER>/idea/tools/deepcode/deepcode-ast/src/test/java/com/sankuai/deepcode/ast/html/test.html";

        String code = new String(Files.readAllBytes(Paths.get(xmlFilePath)));
        HTMLLexer lexer = new HTMLLexer(CharStreams.fromString(code));
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        HTMLParser parser = new HTMLParser(tokens);
        ParseTree tree = parser.htmlDocument();
        MyHtmlVisitor visitor = new MyHtmlVisitor();
        visitor.visit(tree);
        List<HtmlNode> htmlNodeList = visitor.getHtmlNodes();

        List<HtmlNode> htmlNodes= new ArrayList<>();
        List<ScssNode> scssNodes    = new ArrayList<>();
        List<ScriptNode> scriptNodes  = new ArrayList<>();
        List<Integer> commentLines   = new ArrayList<>();
        String filePath = "";
        Map<String, List<ScriptImport>> fileImportFileMap= new HashMap<>();
        ForeEndAnalysis.initListByHtmlNodes(htmlNodeList, htmlNodes, scssNodes, scriptNodes, commentLines, filePath, fileImportFileMap);


        System.out.println();
    }
}

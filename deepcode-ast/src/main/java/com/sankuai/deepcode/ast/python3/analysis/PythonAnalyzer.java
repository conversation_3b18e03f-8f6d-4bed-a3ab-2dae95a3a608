package com.sankuai.deepcode.ast.python3.analysis;

import com.google.common.collect.Lists;
import com.sankuai.deepcode.ast.enums.ChangeTypeEnum;
import com.sankuai.deepcode.ast.enums.DiffTypeEnum;
import com.sankuai.deepcode.ast.model.base.CodeView;
import com.sankuai.deepcode.ast.model.base.CompareRes;
import com.sankuai.deepcode.ast.model.base.GitDiffInfo;
import com.sankuai.deepcode.ast.model.base.LocationInfo;
import com.sankuai.deepcode.ast.model.java.FileNode;
import com.sankuai.deepcode.ast.model.python3.*;
import com.sankuai.deepcode.ast.python3.gen.PythonLexer;
import com.sankuai.deepcode.ast.python3.gen.PythonParser;
import com.sankuai.deepcode.ast.python3.visitor.PythonModuleVisitor;
import com.sankuai.deepcode.ast.util.CompareUtil;
import com.sankuai.deepcode.ast.util.FileUtil;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.Token;
import org.antlr.v4.runtime.tree.ParseTree;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.sankuai.deepcode.ast.util.CompareUtil.WORKSPACE_PATH;


@Getter
@Slf4j
public class PythonAnalyzer {
    private static String FROM_WORKSPACE = "";
    private static String TO_WORKSPACE = "";
    private static String BUILD_WORKSPACE = "";

    @SneakyThrows
    public static PythonAnalysesResult analyzePythonProject(long uniqueId, String gitUrl, String gitName, String fromBranch, String toBranch, String buildBranch, String buildCommit) {
        if (StringUtils.isEmpty(fromBranch)) {
            throw new IllegalArgumentException("需要分析项目的 fromBranch 源分支不能为空");
        }
        if (StringUtils.isEmpty(fromBranch)) {
            fromBranch = "master";
            log.warn("toBranch目标分支为空, 默认master");
        }
        log.info("开始解析python项目:{}, fromBranch: {}, toBranch: {}", gitUrl, fromBranch, toBranch);
        CompareRes compareRes = CompareUtil.getDiffInfos(uniqueId, gitUrl, gitName, fromBranch, toBranch, buildBranch, buildCommit);

        setWorkspace(compareRes);
        PythonAnalysesResult result = analyzePythonProject(compareRes);
        FileUtil.deleteFile(WORKSPACE_PATH, String.valueOf(uniqueId));
        return result;
    }


    public static PythonAnalysesResult analyzePythonProject(CompareRes compareRes) {
        PythonAnalysesResult result = analyzeDiffPythonProject(compareRes);

        result.setFromCommit(compareRes.getFromCommit());
        result.setToCommit(compareRes.getToCommit());
        result.setBuildCommit(compareRes.getBuildCommit());

        result.setFileMap(compareRes.getGitPythonDiffInfoMap().entrySet().stream().collect(
                Collectors.toMap(
                        entry -> compareRes.getGitPath() + "/" + entry.getKey(),
                        entry -> FileNode.of(entry.getValue())
                ))
        );

        return handleAnalyzeResult(result);
    }

    private static PythonAnalysesResult analyzeDiffPythonProject(CompareRes compareRes) {
        PythonAnalysesResult result = new PythonAnalysesResult();
        long start = System.currentTimeMillis();

        compareRes.getGitPythonDiffInfoMap().entrySet().stream()
                // 过滤的文件不进行分析
                .filter(entry -> !compareRes.getFilterPath().contains(entry.getKey()))
                .forEach(entry -> analyzePythonFile(entry.getKey(), entry.getValue(), result));

        result.setAnalyseDuration(System.currentTimeMillis() - start);
        return result;
    }


    /**
     * 补齐需要二次循环的调用关系的操作
     * 1. super() 调用的关系补齐 // TODO
     * 2. 模块内，子域先调用，父域后定义 调用关系补齐 // TODO
     *
     * @param result
     * @return
     */
    private static PythonAnalysesResult handleAnalyzeResult(final PythonAnalysesResult result) {
        // 1. 通过 scopeManager 中的 key 获取当前工程拥有的 module 列表
        Set<String> localModules = new HashSet<>();

        // 从 scopeManagerMap 中获取所有模块路径
        if (result.getScopeManagerMap() != null) {
            localModules = result.getScopeManagerMap().keySet();
        }

        // 如果 scopeManagerMap 为空，则从 moduleMap 中获取模块列表作为备选
        if (localModules.isEmpty() && result.getModuleMap() != null) {
            localModules = result.getModuleMap().keySet();
        }

        System.out.println("当前工程模块列表: " + localModules);

        // 2. 二次处理【模块导入功能】，确认被导入模块/类是工程内/外
        processImportNodes(result, localModules);

        // 3. 二次处理【函数调用】，确认被调用函数是工程内/外
        processFunctionCallNodes(result, localModules);

        return result;
    }

    /**
     * 处理导入节点，设置 source 字段
     *
     * @param result 分析结果
     * @param localModules 本工程模块列表
     */
    private static void processImportNodes(PythonAnalysesResult result, Set<String> localModules) {
        System.out.println("开始处理导入节点，设置 source 字段");
        System.out.println("本工程模块数量: " + localModules.size());

        if (result.getModuleMap() == null) {
            return;
        }

        for (PythonModuleNode moduleNode : result.getModuleMap().values()) {
            // 处理模块级导入
            if (moduleNode.getImportNodes() != null) {
                processImportNodesInList(moduleNode.getImportNodes(), localModules, result);
            }

            // 处理类中的导入
            if (moduleNode.getClassNodes() != null) {
                for (PythonClassNode classNode : moduleNode.getClassNodes()) {
                    if (classNode.getInnerImports() != null) {
                        processImportNodesInList(classNode.getInnerImports(), localModules, result);
                    }

                    // 处理类方法中的导入
                    if (classNode.getMethods() != null) {
                        for (PythonMethodNode methodNode : classNode.getMethods()) {
                            if (methodNode.getInnerImports() != null) {
                                processImportNodesInList(methodNode.getInnerImports(), localModules, result);
                            }
                        }
                    }
                }
            }

            // 处理函数中的导入
            if (moduleNode.getFunctionNodes() != null) {
                for (PythonFunctionNode functionNode : moduleNode.getFunctionNodes()) {
                    if (functionNode.getInnerImports() != null) {
                        processImportNodesInList(functionNode.getInnerImports(), localModules, result);
                    }
                }
            }
        }
    }

    /**
     * 处理导入节点列表
     *
     * @param importNodes 导入节点列表
     * @param localModules 本工程模块列表
     * @param result 分析结果
     */
    private static void processImportNodesInList(List<PythonImportNode> importNodes, Set<String> localModules, PythonAnalysesResult result) {
        for (PythonImportNode importNode : importNodes) {
            // 设置 source 字段
            String importModulePath = importNode.getFromModulePath() != null ?
                importNode.getFromModulePath() : importNode.getFullImportModulePath();

            if (isLocalModule(importModulePath, localModules)) {
                importNode.setSource("local");
            } else {
                importNode.setSource("unknown");
            }

            // 处理 from xxx import * 的情况
            if (importNode.getImportType() == PythonImportNode.ImportType.FROM &&
                importNode.getImportedItems() != null &&
                importNode.getImportedItems().contains("*")) {

                handleStarImport(importNode, result);
            }
        }
    }

    /**
     * 处理 from xxx import * 的情况
     * 从 scopeManager 中获取 xxx 模块的所有符号，添加到当前作用域
     *
     * @param importNode 导入节点
     * @param result 分析结果
     */
    private static void handleStarImport(PythonImportNode importNode, PythonAnalysesResult result) {
        String fromModulePath = importNode.getFromModulePath();
        String currentModulePath = importNode.getCurrentModulePath();

        // 获取当前模块的 scopeManager
        ScopeManager scopeManager = result.getScopeManagerMap().get(currentModulePath);
        if (scopeManager == null) {
            System.out.println("未找到模块 " + currentModulePath + " 的 scopeManager");
            return;
        }

        // 获取目标模块的 scopeManager
        ScopeManager targetScopeManager = result.getScopeManagerMap().get(fromModulePath);
        if (targetScopeManager == null) {
            System.out.println("未找到目标模块 " + fromModulePath + " 的 scopeManager");
            return;
        }

        // 使用新添加的方法将目标模块的所有符号导入到当前作用域
        // 获取导入语句的行号
        int importLine = importNode.getLocation() != null ? importNode.getLocation().getLine() : 1;

        // 将目标模块的所有符号添加到当前模块的作用域
        scopeManager.importAllSymbolsFromScope(targetScopeManager, fromModulePath, importLine);

        System.out.println("成功处理 from " + fromModulePath + " import * 语句，当前模块: " + currentModulePath);
    }

    /**
     * 判断模块是否为本工程模块
     *
     * @param modulePath 模块路径
     * @param localModules 本工程模块集合
     * @return 是否为本工程模块
     */
    private static boolean isLocalModule(String modulePath, Set<String> localModules) {
        if (modulePath == null || modulePath.isEmpty()) {
            return false;
        }

        // 直接匹配
        if (localModules.contains(modulePath)) {
            return true;
        }

        // 检查是否为本工程模块的子模块
        for (String localModule : localModules) {
            if (modulePath.startsWith(localModule + ".") || localModule.startsWith(modulePath + ".")) {
                return true;
            }
        }

        return false;
    }

    /**
     * 处理函数调用节点，设置 source 字段
     *
     * @param result 分析结果
     * @param localModules 本工程模块列表
     */
    private static void processFunctionCallNodes(PythonAnalysesResult result, Set<String> localModules) {
        System.out.println("开始处理函数调用节点，设置 source 字段");
        System.out.println("本工程模块数量: " + localModules.size());

        if (result.getModuleMap() == null) {
            return;
        }

        for (PythonModuleNode moduleNode : result.getModuleMap().values()) {
            // 处理模块级函数调用
            if (moduleNode.getFunctionCallNodes() != null) {
                processFunctionCallNodesInList(moduleNode.getFunctionCallNodes(), localModules);
            }

            // 处理类中的函数调用（通过类方法）
            if (moduleNode.getClassNodes() != null) {
                for (PythonClassNode classNode : moduleNode.getClassNodes()) {
                    if (classNode.getMethods() != null) {
                        for (PythonMethodNode methodNode : classNode.getMethods()) {
                            if (methodNode.getCallNodes() != null) {
                                processFunctionCallNodesInList(methodNode.getCallNodes(), localModules);
                            }
                        }
                    }
                }
            }

            // 处理函数中的函数调用
            if (moduleNode.getFunctionNodes() != null) {
                for (PythonFunctionNode functionNode : moduleNode.getFunctionNodes()) {
                    if (functionNode.getCallNodes() != null) {
                        processFunctionCallNodesInList(functionNode.getCallNodes(), localModules);
                    }
                }
            }
        }
    }

    /**
     * 处理函数调用节点列表
     *
     * @param functionCallNodes 函数调用节点列表
     * @param localModules 本工程模块列表
     */
    private static void processFunctionCallNodesInList(List<PythonFunctionCallNode> functionCallNodes, Set<String> localModules) {
        for (PythonFunctionCallNode callNode : functionCallNodes) {
            String sourceModulePath = callNode.getSourceModulePath();

            if (isLocalModule(sourceModulePath, localModules)) {
                callNode.setSource("local");
            } else {
                callNode.setSource("unknown");
            }
        }
    }

    @SneakyThrows
    public static PythonAnalysesResult analyzePythonProject(String projectPath) {
        PythonAnalysesResult result = new PythonAnalysesResult();

        long start = System.currentTimeMillis();
        log.info("[root]开始解析python项目:{}, start: {}", projectPath, start);
        Path rootPath = Paths.get(projectPath).toAbsolutePath();

        // 需要进行python 分析的文件
        List<Path> filesToAnalyze = Lists.newArrayList();
        try (Stream<Path> paths = Files.walk(rootPath)) {
            paths.filter(Files::isRegularFile)
                    .forEach(path -> {
                        if (path.toString().endsWith(".py")) {
                            filesToAnalyze.add(path);
                        }
                    });
        }

        filesToAnalyze.forEach(path -> analyzePythonFile(rootPath, path, result));
        result.setAnalyseDuration(System.currentTimeMillis() - start);
        log.info("[root]解析python项目完成, cost: {} ms", result.getAnalyseDuration());
        return handleAnalyzeResult(result);
    }

    @SneakyThrows
    private static void analyzePythonFile(String fileName, GitDiffInfo diffInfo, PythonAnalysesResult result) {
        String modulePath = getModulePath(fileName);

        //未变更、增加的代码内容，即为 from 分支上的代码
        String codeViewOfFrom = diffInfo.getCodeViews().stream().filter(
                c -> c.getType() == DiffTypeEnum.SAM.getCode() || c.getType() == DiffTypeEnum.ADD.getCode()
        ).map(CodeView::getView).collect(Collectors.joining("\n"));
        if (StringUtils.isBlank(codeViewOfFrom)) {
            return;
        }

        long start = System.currentTimeMillis();
        // 解析 from 分支上的代码
        PythonLexer lexer = new PythonLexer(CharStreams.fromString(codeViewOfFrom));
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        PythonParser parser = new PythonParser(tokens);
        ParseTree tree = parser.file_input();


        PythonModuleNode moduleNode = new PythonModuleNode()
                .setModuleName(getModuleName(modulePath))
                .setFileName(fileName)
                .setParentModule(getParentModule(modulePath));

        PythonModuleVisitor moduleVisitor = new PythonModuleVisitor(moduleNode, result);
        moduleVisitor.visit(tree);
        result.getModuleMap().put(moduleVisitor.getModuleNode().getModulePath(), moduleVisitor.getModuleNode());

        // 解析隐藏通道中的 注释行信息并分配到各个节点
        assignCommentsToNodes(moduleNode, tokens);

        // 如果整个文件是新增，直接将所有 module/class/funcDef 修改为新增
        if (diffInfo.getChangeType() == ChangeTypeEnum.ADD.getCode()) {
            handleAddFile(moduleNode);
        } else if (diffInfo.getChangeType() == ChangeTypeEnum.CHANGE.getCode()) {
            // 存在变更时，需要解析一遍 to 的代码，再对比两个解析结果
            String codeViewOfTo = diffInfo.getCodeViews().stream().filter(
                    c -> c.getType() == DiffTypeEnum.SAM.getCode() || c.getType() == DiffTypeEnum.DEL.getCode()
            ).map(CodeView::getView).collect(Collectors.joining("\n"));

            PythonAnalysesResult resultTo = new PythonAnalysesResult();

            // 解析 from 分支上的代码
            PythonLexer lexerTo = new PythonLexer(CharStreams.fromString(codeViewOfTo));
            CommonTokenStream tokensTo = new CommonTokenStream(lexerTo);
            PythonParser parserTo = new PythonParser(tokensTo);
            ParseTree treeTo = parserTo.file_input();

            PythonModuleNode moduleNodeTo = new PythonModuleNode()
                    .setModuleName(getModuleName(modulePath))
                    .setFileName(fileName)
                    .setParentModule(getParentModule(modulePath));

            PythonModuleVisitor moduleVisitorTo = new PythonModuleVisitor(moduleNodeTo, resultTo);
            moduleVisitorTo.visit(treeTo);

            // 对比 moduleNode 和 moduleNodeTo 并添加相应处理
            handleModifyFile(diffInfo, moduleNode, moduleNodeTo);
        }

        log.info("[file]解析python文件完成: {}, 解析耗时： {}ms", fileName, System.currentTimeMillis() - start);
    }

    private static void handleModifyFile(GitDiffInfo diffInfo, PythonModuleNode moduleNode, PythonModuleNode moduleNodeTo) {
        // 先形成相应 Map 收集
        moduleNode.populateNodeMaps();
        moduleNodeTo.populateNodeMaps();

        // 类定义的 新增
        Set<String> addClassModulePaths = SetUtils.difference(moduleNode.getClassNodeMap().keySet(), moduleNodeTo.getClassNodeMap().keySet());
        moduleNode.getClassNodeMap().entrySet().stream().filter(entry -> addClassModulePaths.contains(entry.getKey()))
                .forEach(entry -> {
                    entry.getValue().setChangeType(ChangeTypeEnum.ADD);
                    entry.getValue().setChangeLines(IntStream.range(entry.getValue().getStart().getLine(), entry.getValue().getEnd().getLine() + 1).boxed().collect(Collectors.toList()));
                });
        // 类定义的【可能修改】
        Set<String> possiblyModifyClassModulePaths = SetUtils.intersection(moduleNodeTo.getClassNodeMap().keySet(), moduleNode.getClassNodeMap().keySet());
        moduleNode.getClassNodeMap().entrySet().stream().filter(entry -> possiblyModifyClassModulePaths.contains(entry.getKey()))
                .forEach(entry -> {
                    // 类主体字符串不相等时，类存在变更
                    if (!Objects.equals(entry.getValue().getClassBody(), moduleNodeTo.getClassNodeMap().get(entry.getKey()).getClassBody())) {
                        entry.getValue().setChangeType(ChangeTypeEnum.CHANGE);
                        // 变更行需要和 diffInfo 求交集
                        Set<Integer> classLines = IntStream.range(entry.getValue().getStart().getLine(), entry.getValue().getEnd().getLine() + 1).boxed().collect(Collectors.toSet());
                        Set<Integer> changeLines = diffInfo.getChangeLines().stream().filter(classLines::contains).collect(Collectors.toCollection(TreeSet::new));
                        entry.getValue().setChangeLines(Lists.newArrayList(changeLines));
                    }
                });

        // 方法定义的 新增
        Set<String> addFuncDefModulePaths = SetUtils.difference(moduleNode.getFunctionNodeMap().keySet(), moduleNodeTo.getFunctionNodeMap().keySet());
        moduleNode.getFunctionNodeMap().entrySet().stream().filter(entry -> addFuncDefModulePaths.contains(entry.getKey()))
                .forEach(entry -> {
                    entry.getValue().setChangeType(ChangeTypeEnum.ADD);
                    entry.getValue().setChangeLines(IntStream.range(entry.getValue().getStart().getLine(), entry.getValue().getEnd().getLine() + 1).boxed().collect(Collectors.toList()));
                });
        // 方法定义的【可能修改】
        Set<String> possiblyModifyFuncDefModulePaths = SetUtils.intersection(moduleNodeTo.getFunctionNodeMap().keySet(), moduleNode.getFunctionNodeMap().keySet());
        moduleNode.getFunctionNodeMap().entrySet().stream().filter(entry -> possiblyModifyFuncDefModulePaths.contains(entry.getKey()))
                .forEach(entry -> {
                    // 方法主体字符串不相等时，方法存在变更
                    if (!Objects.equals(entry.getValue().getFuncBody(), moduleNodeTo.getFunctionNodeMap().get(entry.getKey()).getFuncBody())) {
                        entry.getValue().setChangeType(ChangeTypeEnum.CHANGE);
                        // 变更行需要和 diffInfo 求交集
                        Set<Integer> funcLines = IntStream.range(entry.getValue().getStart().getLine(), entry.getValue().getEnd().getLine() + 1).boxed().collect(Collectors.toSet());
                        Set<Integer> changeLines = diffInfo.getChangeLines().stream().filter(funcLines::contains).collect(Collectors.toCollection(TreeSet::new));
                        entry.getValue().setChangeLines(Lists.newArrayList(changeLines));
                    }
                });
    }

    /**
     * 处理新增文件
     *
     * @param moduleNode 模块节点
     */
    private static void handleAddFile(PythonModuleNode moduleNode) {
        moduleNode.setChangeType(ChangeTypeEnum.ADD);

        moduleNode.getClassNodes().forEach(classNode -> {
            classNode.setChangeType(ChangeTypeEnum.ADD);
            classNode.setChangeLines(IntStream.range(classNode.getStart().getLine(), classNode.getEnd().getLine() + 1).boxed().collect(Collectors.toList()));
            classNode.getMethods().forEach(methodNode -> {
                methodNode.setChangeType(ChangeTypeEnum.ADD);
                methodNode.setChangeLines(IntStream.range(methodNode.getStart().getLine(), methodNode.getEnd().getLine() + 1).boxed().collect(Collectors.toList()));
            });
        });
        moduleNode.getFunctionNodes().forEach(functionNode -> {
            functionNode.setChangeType(ChangeTypeEnum.ADD);
            functionNode.setChangeLines(IntStream.range(functionNode.getStart().getLine(), functionNode.getEnd().getLine() + 1).boxed().collect(Collectors.toList()));
        });
    }

    @SneakyThrows
    public static void analyzePythonFile(Path rootPath, Path pythonFile, PythonAnalysesResult result) {
        if (Files.notExists(pythonFile)) {
            log.error("需要解析的文件不存在: {}", pythonFile);
            return;
        }
        long start = System.currentTimeMillis();
        log.info("[file]开始解析python文件: {}", pythonFile);
        System.out.println("[file]开始解析python文件:" + pythonFile);
        String content = new String(Files.readAllBytes(pythonFile));
        String relativePath = rootPath.relativize(pythonFile).toString();
        String modulePath = getModulePath(relativePath);

        // Create lexer and parser
        PythonLexer lexer = new PythonLexer(CharStreams.fromString(content));
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        PythonParser parser = new PythonParser(tokens);
        ParseTree tree = parser.file_input();

        PythonModuleNode moduleNode = new PythonModuleNode()
                .setModuleName(getModuleName(modulePath))
                .setFileName(relativePath)
                .setParentModule(getParentModule(modulePath));

        // Module visitor
        PythonModuleVisitor moduleVisitor = new PythonModuleVisitor(moduleNode, result);
        moduleVisitor.visit(tree);
        result.getModuleMap().put(moduleVisitor.getModuleNode().getModulePath(), moduleVisitor.getModuleNode());

        // 解析隐藏通道中的 注释行信息并分配到各个节点
        assignCommentsToNodes(moduleNode, tokens);

        handleAnalyzeResult(result);

        log.info("[file]解析python文件完成: {}, 解析耗时： {}ms", pythonFile, System.currentTimeMillis() - start);
    }

    private static String getModulePath(String relativePath) {
        return relativePath.replace(File.separator, ".")
                .replaceAll("\\.py$", "")
                // 如果是 __init__.py ，则模块名称需要使用目录名称，去除文件名称对应的模块
                .replace(".__init__", "");
    }

    private static String getModuleName(String modulePath) {
        int lastDotIndex = modulePath.lastIndexOf('.');
        return lastDotIndex == -1 ? modulePath : modulePath.substring(lastDotIndex + 1);
    }

    private static String getParentModule(String modulePath) {
        int lastDotIndex = modulePath.lastIndexOf('.');
        return lastDotIndex == -1 ? "" : modulePath.substring(0, lastDotIndex);
    }

    /**
     * 将注释分配到对应的节点
     */
    private static void assignCommentsToNodes(PythonModuleNode moduleNode, CommonTokenStream tokens) {
        // 从隐藏通道提取注释
        List<PythonComment> comments = extractComments(tokens);

        // 为每个注释找到合适的归属节点
        for (PythonComment comment : comments) {
            int commentLine = comment.commentLine();
            if (commentLine == -1) {
                continue;
            }

            boolean assigned = false;

            // 1. 首先检查是否在模块级函数内部
            for (PythonFunctionNode functionNode : moduleNode.getFunctionNodes()) {
                if (isCommentInRange(commentLine, functionNode.getStart(), functionNode.getEnd())) {
                    functionNode.getComments().add(comment);
                    assigned = true;
                    break;
                }
            }

            if (assigned) {
                continue;
            }

            // 2. 检查是否在类内部
            for (PythonClassNode classNode : moduleNode.getClassNodes()) {
                if (isCommentInRange(commentLine, classNode.getStart(), classNode.getEnd())) {
                    // 检查是否在类的方法内部
                    boolean inMethod = false;
                    for (PythonMethodNode methodNode : classNode.getMethods()) {
                        if (isCommentInRange(commentLine, methodNode.getStart(), methodNode.getEnd())) {
                            methodNode.getComments().add(comment);
                            inMethod = true;
                            assigned = true;
                            break;
                        }
                    }

                    // 如果不在方法内，则属于类
                    if (!inMethod) {
                        classNode.getComments().add(comment);
                        assigned = true;
                    }
                    break;
                }
            }

            // 3. 如果都不匹配，则属于模块
            if (!assigned) {
                moduleNode.getComments().add(comment);
            }
        }
    }

    /**
     * 检查注释是否在指定范围内
     */
    private static boolean isCommentInRange(int commentLine, LocationInfo start, LocationInfo end) {
        if (start == null || end == null) {
            return false;
        }
        return commentLine >= start.getLine() && commentLine <= end.getLine();
    }


    /**
     * 从token流中提取注释
     */
    private static List<PythonComment> extractComments(CommonTokenStream tokens) {
        List<PythonComment> comments = new ArrayList<>();
        for (Token token : tokens.getTokens()) {
            if (token.getChannel() == Token.HIDDEN_CHANNEL) {
                String text = token.getText().trim();
                if (StringUtils.isNotEmpty(text) && text.startsWith("#")) {
                    PythonComment comment = new PythonComment();
                    // 设置起始位置
                    comment.setStart(new LocationInfo()
                            .setLine(token.getLine())
                            .setColumn(token.getCharPositionInLine()));
                    // 设置结束位置（单行注释的结束位置就是同一行的末尾）
                    comment.setEnd(new LocationInfo()
                            .setLine(token.getLine())
                            .setColumn(token.getCharPositionInLine() + text.length()));
                    comment.setComment(text);
                    comments.add(comment);
                }
            }
        }
        return comments;
    }


    private static String getModuleName(Path rootPath, Path pythonFile) {
        Path relativePath = rootPath.relativize(pythonFile);
        String fileName = relativePath.getFileName().toString();

        if (fileName.equals("__init__.py")) {
            return relativePath.getParent() == null ? "" :
                    relativePath.getParent().toString().replace(File.separator, ".");
        }

        String fileNameWithoutExtension = fileName.substring(0, fileName.lastIndexOf('.'));

        // Convert file separators to dots and remove the file name
        String packageName = relativePath.getParent() == null ? "" :
                relativePath.getParent().toString().replace(File.separator, ".");

        // Combine package name and file name
        return packageName.isEmpty() ? fileNameWithoutExtension : packageName + "." + fileNameWithoutExtension;
    }

    private static void setWorkspace(CompareRes compareRes) {
        FROM_WORKSPACE = compareRes.getGitPath();
        TO_WORKSPACE = compareRes.getGitToPath();
        if (compareRes.isCheck()) {
            BUILD_WORKSPACE = compareRes.getGitPath();
        }
    }
}